 PASS  shared/src/base/__tests__/modules/event-handler-registry/DeduplicationEngine.test.ts (447 MB heap size)
  DeduplicationEngine Module Testing
    Core Functionality
      ✓ should initialize DeduplicationEngine with default configuration (10 ms)
      ✓ should initialize DeduplicationEngine with custom configuration (3 ms)
      ✓ should provide memory-safe resource management (3 ms)
      ✓ should handle basic deduplication operations (4 ms)
    Deduplication Strategies
      Signature-based Deduplication
        ✓ should detect duplicate handlers using signature strategy (4 ms)
        ✓ should not detect non-duplicate handlers using signature strategy (4 ms)
        ✓ should handle signature-based deduplication with different metadata (4 ms)
      Reference-based Deduplication
        ✓ should detect duplicate handlers using reference strategy (8 ms)
        ✓ should not detect non-duplicate handlers using reference strategy (3 ms)
      Custom Deduplication
        ✓ should use custom deduplication function when provided (4 ms)
        ✓ should fall back to signature strategy when no custom function provided (3 ms)
        ✓ should handle custom function returning false (3 ms)
    Handler Registration and Removal
      ✓ should register handler signature successfully (2 ms)
      ✓ should remove handler signature successfully (2 ms)
      ✓ should handle removal of non-existent handler gracefully (6 ms)
      ✓ should register multiple handlers and maintain accurate count (2 ms)
    Performance Validation
      ✓ should meet <1ms deduplication check requirement (4 ms)
      ✓ should handle high-volume deduplication checks efficiently (57 ms)
      ✓ should maintain performance with large signature cache (19 ms)
      ✓ should validate performance requirements for different strategies (4 ms)
    Error Handling
      ✓ should handle unknown deduplication strategy (52 ms)
      ✓ should handle custom deduplication function errors (3 ms)
      ✓ should handle malformed handler data gracefully (2 ms)
      ✓ should handle edge cases with empty or null metadata (2 ms)
      ✓ should handle circular reference in metadata (6 ms)
    Resilient Timing Integration
      ✓ should initialize resilient timing infrastructure correctly (3 ms)
      ✓ should use timing context lifecycle correctly during deduplication (2 ms)
      ✓ should record timing metrics for successful deduplication operations (2 ms)
      ✓ should record error metrics during failed deduplication operations (2 ms)
      ✓ should handle timing context cleanup in error paths (2 ms)
      ✓ should validate Jest fake timer compatibility (2 ms)
      ✓ should handle timing infrastructure failures gracefully (3 ms)
      ✓ should validate timing reliability and baseline configuration (2 ms)
    Metrics and Monitoring
      ✓ should track deduplication check metrics correctly (2 ms)
      ✓ should track cache hit metrics for duplicate detection (3 ms)
      ✓ should calculate rolling average check time (5 ms)
      ✓ should provide comprehensive metrics snapshot (2 ms)
      ✓ should reset deduplication metrics correctly (1 ms)
    Cache Management
      ✓ should handle cache expiry correctly (2 ms)
      ✓ should enforce maximum cache size limits (2 ms)
      ✓ should maintain cache consistency during operations (2 ms)
      ✓ should handle concurrent cache operations safely (9 ms)
    Memory Safety and Lifecycle
      ✓ should properly initialize and shutdown (1 ms)
      ✓ should clean up resources on shutdown (1 ms)
      ✓ should handle multiple initialization calls safely (2 ms)
      ✓ should handle shutdown without initialization (2 ms)
      ✓ should maintain memory boundaries under load (8 ms)
    Coverage Enhancement Tests
      ✓ should cover all configuration combinations (6 ms)
      ✓ should handle edge cases in signature generation (2 ms)
      ✓ should handle reference-based deduplication with identical callbacks (2 ms)
      ✓ should validate metrics update accuracy with edge cases (1 ms)
      ✓ should handle custom deduplication with no matching handlers (2 ms)
      ✓ should cover configuration validation edge cases (2 ms)
      ✓ should handle timing errors during deduplication (2 ms)
    Comprehensive Coverage Tests
      ✓ should trigger cache cleanup interval callback (Line 143) (2 ms)
      ✓ should reach default case in strategy switch (Line 206) (2 ms)
      ✓ should cover cache hit metrics update condition (Line 486) (8 ms)
      ✓ should cover cache miss metrics update condition (Line 488) (3 ms)
      ✓ should handle metrics update edge cases with multiple operations (2 ms)
      ✓ should handle timing infrastructure edge cases (2 ms)

-------------------------------------|---------|----------|---------|---------|----------------------------------------------------------------
File                                 | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                              
-------------------------------------|---------|----------|---------|---------|----------------------------------------------------------------
All files                            |      44 |    39.33 |   33.98 |    44.6 |                                                                
 base                                |   28.96 |    19.44 |   22.64 |   29.68 |                                                                
  MemorySafeResourceManager.ts       |   28.96 |    19.44 |   22.64 |   29.68 | ...538,561-624,660-818,830-831,851-883,903-916,923-938,954-973 
 base/event-handler-registry/modules |   97.43 |    95.12 |      95 |   98.26 |                                                                
  DeduplicationEngine.ts             |   97.43 |    95.12 |      95 |   98.26 | 143,206                                                        
 base/utils                          |   25.49 |    16.21 |   13.33 |   25.49 |                                                                
  ResilientMetrics.ts                |   25.49 |    16.21 |   13.33 |   25.49 | 135-184,199-200,224-281,290-328,337-376,394-410,418-429        
-------------------------------------|---------|----------|---------|---------|----------------------------------------------------------------
Test Suites: 1 passed, 1 total
Tests:       60 passed, 60 total
Snapshots:   0 total
Time:        4.32 s
Ran all test suites matching /DeduplicationEngine.test.ts/i.