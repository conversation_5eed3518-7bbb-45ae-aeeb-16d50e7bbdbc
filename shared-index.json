[{"type": "directory", "name": "shared", "contents": [{"type": "directory", "name": "src", "contents": [{"type": "directory", "name": "base", "contents": [{"type": "directory", "name": "atomic-circular-buffer-enhanced", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "BufferAnalyticsEngine.ts"}, {"type": "file", "name": "BufferConfigurationManager.ts"}, {"type": "file", "name": "BufferOperationsManager.ts"}, {"type": "file", "name": "BufferPersistenceManager.ts"}, {"type": "file", "name": "BufferStrategyManager.ts"}, {"type": "file", "name": "BufferUtilities.ts"}]}, {"type": "file", "name": "performance-validation.ts"}]}, {"type": "file", "name": "AtomicCircularBufferEnhanced.ts"}, {"type": "file", "name": "AtomicCircularBuffer.ts"}, {"type": "file", "name": "CleanupCoordinatorEnhanced.ts"}, {"type": "directory", "name": "event-handler-registry", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "ComplianceManager.ts"}, {"type": "file", "name": "DeduplicationEngine.ts"}, {"type": "file", "name": "EventBuffering.ts"}, {"type": "file", "name": "EventEmissionSystem.ts"}, {"type": "file", "name": "EventUtilities.ts"}, {"type": "file", "name": "MetricsManager.ts"}, {"type": "file", "name": "MiddlewareManager.ts"}]}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "EventConfiguration.ts"}, {"type": "file", "name": "EventHandlerEnhancedTypes.ts"}, {"type": "file", "name": "EventTypes.ts"}]}]}, {"type": "file", "name": "EventHandlerRegistryEnhanced.ts"}, {"type": "file", "name": "EventHandlerRegistry.ts"}, {"type": "file", "name": "LoggingMixin.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.ts"}, {"type": "file", "name": "MemorySafeResourceManager.ts"}, {"type": "directory", "name": "memory-safety-manager", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "ComponentDiscoveryManager.ts"}, {"type": "file", "name": "ComponentIntegrationEngine.ts"}, {"type": "file", "name": "EnhancedConfigurationManager.ts"}, {"type": "file", "name": "EnhancedMetricsCollector.ts"}, {"type": "file", "name": "SystemCoordinationManager.ts"}, {"type": "file", "name": "SystemStateManager.ts"}]}]}, {"type": "file", "name": "MemorySafetyManagerEnhanced.ts"}, {"type": "file", "name": "MemorySafetyManager.ts"}, {"type": "directory", "name": "modules", "contents": [{"type": "directory", "name": "cleanup", "contents": [{"type": "file", "name": "CleanupConfiguration.ts"}, {"type": "file", "name": "CleanupTemplateManager.ts"}, {"type": "file", "name": "CleanupUtilities.ts"}, {"type": "file", "name": "DependencyResolver.ts"}, {"type": "file", "name": "RollbackManager.ts"}, {"type": "file", "name": "RollbackSnapshots.ts"}, {"type": "file", "name": "RollbackUtilities.ts"}, {"type": "file", "name": "SystemOrchestrator.ts"}, {"type": "file", "name": "TemplateDependencies.ts"}, {"type": "file", "name": "TemplateValidation.ts"}, {"type": "file", "name": "TemplateWorkflows.ts"}, {"type": "file", "name": "UtilityAnalysis.ts"}, {"type": "file", "name": "UtilityExecution.ts"}, {"type": "file", "name": "UtilityPerformance.ts"}, {"type": "file", "name": "UtilityValidation.ts"}]}]}, {"type": "file", "name": "refactoring-prompt.md"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "AtomicCircularBuffer.core.test.ts"}, {"type": "file", "name": "AtomicCircularBufferEnhanced.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.integration.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.memory.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.performance.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.test.ts"}, {"type": "file", "name": "CleanupCoordinatorEnhanced.test.ts"}, {"type": "file", "name": "EventHandlerRegistryEnhanced.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.integration.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.memory-leak.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.test.ts"}, {"type": "file", "name": "LoggingMixin.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.integration.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.performance.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.test.ts"}, {"type": "file", "name": "MemorySafeSystem.integration.test.ts"}, {"type": "file", "name": "MemorySafetyManagerEnhanced.test.ts"}, {"type": "directory", "name": "modules", "contents": [{"type": "directory", "name": "atomic-circular-buffer", "contents": [{"type": "file", "name": "BufferStrategyManager.test.ts"}]}, {"type": "directory", "name": "cleanup", "contents": [{"type": "file", "name": "CleanupConfiguration.test.ts"}, {"type": "file", "name": "CleanupTemplateManager.test.ts"}, {"type": "file", "name": "PerformanceValidation.test.ts"}, {"type": "file", "name": "RollbackManager.test.ts"}, {"type": "file", "name": "RollbackSnapshots.test.ts"}, {"type": "file", "name": "RollbackUtilities.test.ts"}, {"type": "file", "name": "SystemOrchestrator.test.ts"}, {"type": "file", "name": "TemplateDependencies.test.ts"}, {"type": "file", "name": "TemplateValidation.test.ts"}, {"type": "file", "name": "TemplateWorkflows.test.ts"}]}, {"type": "directory", "name": "event-handler-registry", "contents": [{"type": "file", "name": "EventEmissionSystem.test.ts"}]}, {"type": "directory", "name": "timer-coordination", "contents": [{"type": "file", "name": "TimerPoolManager.test.ts"}]}]}, {"type": "file", "name": "TimerCoordinationServiceEnhanced.test.ts"}, {"type": "file", "name": "TimerCoordinationService.test.ts"}, {"type": "directory", "name": "types"}, {"type": "directory", "name": "utils", "contents": [{"type": "file", "name": "EnterpriseErrorHandling.test.ts"}, {"type": "file", "name": "JestCompatibilityUtils.test.ts"}]}]}, {"type": "directory", "name": "timer-coordination", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "AdvancedScheduler.ts"}, {"type": "file", "name": "PhaseIntegration.ts"}, {"type": "file", "name": "TimerConfiguration.ts"}, {"type": "file", "name": "TimerCoordinationPatterns.ts"}, {"type": "file", "name": "TimerPoolManager.ts"}, {"type": "file", "name": "TimerUtilities.ts"}]}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "TimerTypes.ts"}]}]}, {"type": "file", "name": "TimerCoordinationServiceEnhanced-original.ts"}, {"type": "file", "name": "TimerCoordinationServiceEnhanced.ts"}, {"type": "file", "name": "TimerCoordinationService.ts"}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "CleanupTypes.ts"}]}, {"type": "directory", "name": "utils", "contents": [{"type": "file", "name": "EnterpriseErrorHandling.ts"}, {"type": "file", "name": "JestCompatibilityUtils.ts"}, {"type": "file", "name": "ResilientMetrics.ts"}, {"type": "file", "name": "ResilientTiming.ts"}]}]}, {"type": "directory", "name": "constants", "contents": [{"type": "directory", "name": "platform", "contents": [{"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "environment-constants-calculator.ts"}, {"type": "file", "name": "tracking-constants-enhanced.ts"}, {"type": "file", "name": "tracking-constants.ts"}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "tracking-management-constants.ts"}]}]}, {"type": "directory", "name": "interfaces", "contents": [{"type": "directory", "name": "governance", "contents": [{"type": "directory", "name": "management-configuration", "contents": [{"type": "file", "name": "governance-rule-documentation-generator.ts"}, {"type": "file", "name": "governance-rule-environment-manager.ts"}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "core-interfaces.ts"}, {"type": "file", "name": "notification-interfaces.ts"}, {"type": "file", "name": "tracking-interfaces.ts"}]}]}, {"type": "directory", "name": "types", "contents": [{"type": "directory", "name": "platform", "contents": [{"type": "directory", "name": "governance", "contents": [{"type": "directory", "name": "automation-engines", "contents": [{"type": "file", "name": "workflow-engines-types.ts"}]}, {"type": "file", "name": "automation-processing-types.ts"}, {"type": "file", "name": "governance-interfaces.ts"}, {"type": "file", "name": "governance-types.ts"}, {"type": "directory", "name": "management-configuration", "contents": [{"type": "file", "name": "documentation-generator-types.ts"}, {"type": "file", "name": "environment-manager-types.ts"}]}, {"type": "file", "name": "notification-types.ts"}, {"type": "file", "name": "resource-interfaces.ts"}, {"type": "file", "name": "rule-management-types.ts"}, {"type": "file", "name": "security-types.ts"}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "directory", "name": "core", "contents": [{"type": "file", "name": "base-types.ts"}, {"type": "file", "name": "tracking-config-types.ts"}, {"type": "file", "name": "tracking-data-types.ts"}, {"type": "file", "name": "tracking-service-types.ts"}]}, {"type": "file", "name": "index.ts"}, {"type": "directory", "name": "specialized", "contents": [{"type": "file", "name": "analytics-types.ts"}, {"type": "file", "name": "authority-types.ts"}, {"type": "file", "name": "orchestration-types.ts"}, {"type": "file", "name": "realtime-types.ts"}, {"type": "file", "name": "validation-types.ts"}]}, {"type": "file", "name": "tracking-types.ts"}, {"type": "directory", "name": "utilities", "contents": [{"type": "file", "name": "error-types.ts"}, {"type": "file", "name": "metrics-types.ts"}, {"type": "file", "name": "workflow-types.ts"}]}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "core-types.ts"}, {"type": "file", "name": "tracking-management-types.ts"}]}]}]}]}, {"type": "report", "directories": 43, "files": 134}]